/* ----------------------------------------------------------------------
 * Project:      CMSIS DSP Library
 * Title:        arm_min_no_idx_f32.c
 * Description:  Minimum value of a floating-point vector without returning the index
 *
 * $Date:        16 November 2021
 * $Revision:    V1.10.0
 *
 * Target Processor: Cortex-M and Cortex-A cores
 * -------------------------------------------------------------------- */
/*
 * Copyright (C) 2010-2021 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "dsp/statistics_functions.h"
#if (defined(ARM_MATH_NEON) || defined(ARM_MATH_MVEF)) && !defined(ARM_MATH_AUTOVECTORIZE)
#include <limits.h>
#endif

/**
  @ingroup groupStats
 */


/**
  @addtogroup Min
  @{
 */

/**
  @brief         Minimum value of a floating-point vector.
  @param[in]     pSrc       points to the input vector
  @param[in]     blockSize  number of samples in input vector
  @param[out]    pResult    minimum value returned here
  @return        none
 */

#if defined(ARM_MATH_MVEF) && !defined(ARM_MATH_AUTOVECTORIZE)

void arm_min_no_idx_f32(
    const float32_t *pSrc,
    uint32_t   blockSize,
    float32_t *pResult)
{
   f32x4_t     vecSrc;
   f32x4_t     curExtremValVec = vdupq_n_f32(F32_MAX);
   float32_t   minValue = F32_MAX;
   float32_t   newVal;
   uint32_t    blkCnt;

   /* Loop unrolling: Compute 4 outputs at a time */
   blkCnt = blockSize >> 2U;

   while (blkCnt > 0U)
   {

        vecSrc = vldrwq_f32(pSrc);
        /*
         * update per-lane min.
         */
        curExtremValVec = vminnmq(vecSrc, curExtremValVec);
        /*
         * Decrement the blockSize loop counter
         * Advance vector source and destination pointers
         */
        pSrc += 4;
        blkCnt --;
    }
    /*
     * Get min value across the vector
     */
    minValue = vminnmvq(minValue, curExtremValVec);

    blkCnt = blockSize & 3;

    while (blkCnt > 0U)
    {
        newVal = *pSrc++;

        /* compare for the minimum value */
        if (minValue > newVal)
        {
            /* Update the minimum value and it's index */
            minValue = newVal;
        }

        blkCnt --;
    }

    *pResult = minValue;
}

#else

void arm_min_no_idx_f32(
    const float32_t *pSrc,
    uint32_t   blockSize,
    float32_t *pResult)
{
   float32_t   minValue = F32_MAX;
   float32_t   newVal;

   while (blockSize > 0U)
   {
       newVal = *pSrc++;
   
       /* compare for the minimum value */
       if (minValue > newVal)
       {
           /* Update the minimum value and it's index */
           minValue = newVal;
       }
   
       blockSize --;
   }
    
   *pResult = minValue;
}

#endif /* defined(ARM_MATH_MVEF) && !defined(ARM_MATH_AUTOVECTORIZE) */

/**
  @} end of Min group
 */
